{"version": 3, "file": "no-empty-object-type.js", "sourceRoot": "", "sources": ["../../src/rules/no-empty-object-type.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAqC;AAsBrC,MAAM,cAAc,GAAG,CAAC,SAAiB,EAAU,EAAE,CACnD;IACE,GAAG,SAAS,0EAA0E;IACtF,uHAAuH;IACvH,gFAAgF;IAChF,gFAAgF;CACjF,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEf,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,qDAAqD;SACnE;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,gBAAgB,EAAE,cAAc,CAAC,gCAAgC,CAAC;YAClE,aAAa,EAAE,cAAc,CAAC,gCAAgC,CAAC;YAC/D,yBAAyB,EACvB,mEAAmE;YACrE,qBAAqB,EAAE,iDAAiD;YACxE,8BAA8B,EAC5B,4CAA4C;YAC9C,sBAAsB,EAAE,sCAAsC;SAC/D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,eAAe,EAAE;wBACf,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,qBAAqB,CAAC;wBAChD,IAAI,EAAE,QAAQ;qBACf;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,CAAC,QAAQ,EAAE,yBAAyB,EAAE,OAAO,CAAC;wBACpD,IAAI,EAAE,QAAQ;qBACf;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,eAAe,EAAE,OAAO;YACxB,gBAAgB,EAAE,OAAO;SAC1B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,eAAe,EAAE,aAAa,EAAE,gBAAgB,EAAE,CAAC;QACpE,MAAM,mBAAmB,GAAG,aAAa;YACvC,CAAC,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC;YAChC,CAAC,CAAC,SAAS,CAAC;QAEd,OAAO;YACL,GAAG,CAAC,eAAe,KAAK,QAAQ,IAAI;gBAClC,sBAAsB,CAAC,IAAI;oBACzB,IAAI,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC5C,OAAO;oBACT,CAAC;oBAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;oBAC5B,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;wBAC3B,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC;4BAClB,eAAe,KAAK,qBAAqB,CAAC;wBAC5C,MAAM,CAAC,MAAM,GAAG,CAAC,EACjB,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,MAAM,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAEhD,MAAM,0BAA0B,GAAG,KAAK,CAAC,GAAG;yBACzC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;wBAClB,EAAE,IAAI,CAAC,IAAI,CACT,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CACzD,CAAC;oBAEJ,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACxB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,EAAE,MAAM,EAAE,iBAAiB,EAAE;4BACnC,IAAI,EAAE,IAAI,CAAC,EAAE;4BACb,SAAS,EAAE,kBAAkB;4BAC7B,GAAG,CAAC,CAAC,0BAA0B,IAAI;gCACjC,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oCACjD,IAAI,EAAE,EAAE,WAAW,EAAE;oCACrB,GAAG,CAAC,KAAK;wCACP,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wCAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc;4CACnC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;4CACjD,CAAC,CAAC,EAAE,CAAC;wCAEP,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,QAAQ,EAAE,GAAG,SAAS,MAAM,WAAW,EAAE,CAC1C,CAAC;oCACJ,CAAC;oCACD,SAAS,EAAE,uBAAuB;iCACnC,CAAC,CAAC;6BACJ,CAAC;yBACH,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,IAAI,CAAC,EAAE;wBACb,SAAS,EAAE,2BAA2B;wBACtC,GAAG,CAAC,CAAC,0BAA0B,IAAI;4BACjC,OAAO,EAAE;gCACP;oCACE,GAAG,CAAC,KAAK;wCACP,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;wCACvD,MAAM,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wCAC/C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc;4CACnC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;4CACjD,CAAC,CAAC,EAAE,CAAC;wCAEP,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,QAAQ,EAAE,GAAG,SAAS,MAAM,QAAQ,EAAE,CACvC,CAAC;oCACJ,CAAC;oCACD,SAAS,EAAE,gCAAgC;iCAC5C;6BACF;yBACF,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;YACF,GAAG,CAAC,gBAAgB,KAAK,QAAQ,IAAI;gBACnC,aAAa,CAAC,IAAI;oBAChB,IACE,IAAI,CAAC,OAAO,CAAC,MAAM;wBACnB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;wBACtD,CAAC,mBAAmB;4BAClB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;4BAC1D,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,EAChD,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,EAAE,MAAM,EAAE,kBAAkB,EAAE;wBACpC,SAAS,EAAE,eAAe;wBAC1B,IAAI;wBACJ,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;4BACjD,IAAI,EAAE,EAAE,WAAW,EAAE;4BACrB,SAAS,EAAE,wBAAwB;4BACnC,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC;yBACvC,CAAC,CAAC;qBACJ,CAAC,CAAC;gBACL,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}