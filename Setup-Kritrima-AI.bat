@echo off
setlocal enabledelayedexpansion

:: ============================================================================
:: KRITRIMA-AI AUTOMATED WSL SETUP SCRIPT
:: ============================================================================
:: Professional Windows 11 WSL automation script for Kritrima-AI development
:: Author: Kritrima-AI Team
:: Version: 1.0.0
:: 
:: This script automatically:
:: - Ensures WSL2 is installed and configured
:: - Sets up Node.js 22+ environment
:: - Installs all dependencies
:: - Builds the project
:: - Creates development shortcuts
:: - Provides easy launch commands
:: ============================================================================

title Kritrima-AI WSL Setup Wizard

:: Configuration
set "PROJECT_NAME=Kritrima-AI"
set "WSL_DISTRO=Ubuntu"
set "NODE_VERSION=22"
set "SOURCE_PATH=%~dp0"
set "WSL_PROJECT_PATH=/home/<USER>/projects/kritrima-ai"
set "ONEDRIVE_PATH=%USERPROFILE%\OneDrive\Documents\Kritrima AI"

echo.
echo ================================================================================
echo                     KRITRIMA-AI WSL SETUP WIZARD
echo                         Professional Automation
echo ================================================================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required!
    echo [WARNING] Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo [SUCCESS] Running with administrator privileges
echo.

:: Step 1: Check and install WSL2
echo [STEP] Step 1: Checking WSL2 installation...
wsl --status >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] WSL2 not found. Installing WSL2...
    dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
    dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
    
    echo [INFO] Downloading WSL2 kernel update...
    powershell -Command "Invoke-WebRequest -Uri 'https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi' -OutFile '%TEMP%\wsl_update_x64.msi'"
    
    echo [INFO] Installing WSL2 kernel update...
    msiexec /i "%TEMP%\wsl_update_x64.msi" /quiet /norestart
    
    wsl --set-default-version 2
    
    echo [SUCCESS] WSL2 installed successfully
    echo [WARNING] System restart required. Please restart and run this script again.
    pause
    exit /b 0
) else (
    echo [SUCCESS] WSL2 is already installed
)

:: Step 2: Check and install Ubuntu
echo.
echo [STEP] Step 2: Checking Ubuntu distribution...
wsl -l -v | findstr "Ubuntu" >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Ubuntu not found. Installing Ubuntu...
    wsl --install -d Ubuntu
    echo [SUCCESS] Ubuntu installed successfully
    echo [WARNING] Please complete Ubuntu setup and run this script again.
    pause
    exit /b 0
) else (
    echo [SUCCESS] Ubuntu distribution is available
)

:: Step 3: Configure WSL for better file system performance
echo.
echo [STEP] Step 3: Configuring WSL for optimal performance...
wsl -d Ubuntu -e bash -c "
if [ ! -f /etc/wsl.conf ]; then
    echo '[automount]' | sudo tee /etc/wsl.conf > /dev/null
    echo 'enabled = true' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'root = /mnt/' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'options = \"metadata,umask=22,fmask=11\"' | sudo tee -a /etc/wsl.conf > /dev/null
    echo '[interop]' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'appendWindowsPath = false' | sudo tee -a /etc/wsl.conf > /dev/null
fi
"
echo [SUCCESS] WSL configuration optimized

:: Step 4: Install Node.js and development tools
echo.
echo [STEP] Step 4: Setting up Node.js %NODE_VERSION% and development environment...
wsl -d Ubuntu -e bash -c "
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential development tools
sudo apt install -y curl wget git build-essential

# Install Node Version Manager (nvm)
if [ ! -d ~/.nvm ]; then
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    export NVM_DIR=\"\$HOME/.nvm\"
    [ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
fi

# Source nvm
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"

# Install Node.js %NODE_VERSION%
nvm install %NODE_VERSION%
nvm use %NODE_VERSION%
nvm alias default %NODE_VERSION%

# Install pnpm globally
npm install -g pnpm

# Verify installations
echo \"Node.js version: \$(node --version)\"
echo \"npm version: \$(npm --version)\"
echo \"pnpm version: \$(pnpm --version)\"
"
echo [SUCCESS] Node.js environment configured

:: Step 5: Copy project to WSL file system
echo.
echo [STEP] Step 5: Setting up Kritrima-AI project in WSL...

:: Check if source exists
if not exist "%SOURCE_PATH%" (
    if exist "%ONEDRIVE_PATH%" (
        set "SOURCE_PATH=%ONEDRIVE_PATH%"
    ) else (
        echo [ERROR] Project source not found!
        echo [WARNING] Please ensure the project is in the same directory as this script or in OneDrive
        pause
        exit /b 1
    )
)

wsl -d Ubuntu -e bash -c "
# Create projects directory
mkdir -p ~/projects

# Remove existing project if it exists
if [ -d ~/projects/kritrima-ai ]; then
    rm -rf ~/projects/kritrima-ai
fi

# Copy project from Windows to WSL
echo 'Copying project files to WSL file system...'
cp -r '/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI' ~/projects/kritrima-ai

# Navigate to project directory
cd ~/projects/kritrima-ai

# Source nvm and set Node.js
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
nvm use %NODE_VERSION%

# Install project dependencies
echo 'Installing project dependencies...'
pnpm install

# Build the project
echo 'Building the project...'
pnpm run build

# Install CLI globally
echo 'Installing Kritrima-AI CLI globally...'
npm install -g .

echo 'Project setup completed successfully!'
"
echo [SUCCESS] Kritrima-AI project configured in WSL

:: Step 6: Create convenience scripts
echo.
echo [STEP] Step 6: Creating convenience scripts and shortcuts...

:: Create launch script for Kritrima-AI
(
echo @echo off
echo title Kritrima-AI CLI
echo echo.
echo echo ================================================================================
echo echo                           KRITRIMA-AI CLI
echo echo ================================================================================
echo echo.
echo echo [INFO] Welcome to Kritrima-AI! Enter your prompt below:
echo echo [EXAMPLE] "Create a simple web page with a contact form"
echo echo.
echo set /p "prompt=Your prompt: "
echo if "!prompt!"=="" ^(
echo     echo [ERROR] No prompt provided
echo     pause
echo     exit /b 1
echo ^)
echo echo.
echo echo [INFO] Launching Kritrima-AI...
echo wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai && export NVM_DIR='\$HOME/.nvm' && [ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh' && nvm use %NODE_VERSION% && kritrima-ai '!prompt!'"
echo pause
) > "Launch-Kritrima-AI.bat"

:: Create development script
(
echo @echo off
echo title Kritrima-AI Development
echo echo.
echo echo ================================================================================
echo echo                      KRITRIMA-AI DEVELOPMENT
echo echo ================================================================================
echo echo.
echo echo [INFO] Starting development environment...
echo wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai && export NVM_DIR='\$HOME/.nvm' && [ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh' && nvm use %NODE_VERSION% && echo 'Development commands:' && echo '  pnpm run dev     - Start development with watch mode' && echo '  pnpm run build   - Build the project' && echo '  pnpm test        - Run tests' && echo '  pnpm run lint    - Run linting' && echo '' && bash"
) > "Kritrima-AI-Development.bat"

:: Create sync script
(
echo @echo off
echo title Sync to OneDrive
echo echo.
echo echo ================================================================================
echo echo                        SYNC TO ONEDRIVE
echo echo ================================================================================
echo echo.
echo echo [INFO] Syncing changes back to OneDrive...
echo wsl -d Ubuntu -e bash -c "rsync -av --exclude node_modules --exclude dist ~/projects/kritrima-ai/ '/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/' && echo 'Sync completed successfully!'"
echo echo [SUCCESS] Sync completed!
echo pause
) > "Sync-to-OneDrive.bat"

:: Create examples runner
(
echo @echo off
echo title Kritrima-AI Examples
echo echo.
echo echo ================================================================================
echo echo                       KRITRIMA-AI EXAMPLES
echo echo ================================================================================
echo echo.
echo echo [INFO] Available examples:
echo echo 1. camerascii     - Webcam ASCII art
echo echo 2. prompt-analyzer - Prompt analysis tool
echo echo 3. impossible-pong - Game development
echo echo.
echo set /p "choice=Select example (1-3): "
echo if "!choice!"=="1" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/camerascii && chmod +x run.sh && ./run.sh"
echo if "!choice!"=="2" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/prompt-analyzer && chmod +x run.sh && ./run.sh"
echo if "!choice!"=="3" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/impossible-pong && chmod +x run.sh && ./run.sh"
echo pause
) > "Run-Examples.bat"

echo [SUCCESS] Convenience scripts created

:: Step 7: Final verification and testing
echo.
echo [STEP] Step 7: Final verification and testing...
wsl -d Ubuntu -e bash -c "
cd ~/projects/kritrima-ai
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
nvm use %NODE_VERSION%

echo 'Verifying installation...'
echo 'Node.js version:' \$(node --version)
echo 'npm version:' \$(npm --version)
echo 'pnpm version:' \$(pnpm --version)
echo 'Kritrima-AI CLI:' \$(which kritrima-ai)

# Test CLI
echo 'Testing Kritrima-AI CLI...'
kritrima-ai --help > /dev/null 2>&1 && echo 'CLI test: PASSED' || echo 'CLI test: FAILED'
"

:: Success message and instructions
echo.
echo ================================================================================
echo                           SETUP COMPLETED!
echo ================================================================================
echo.
echo [INFO] Created convenience scripts:
echo    Launch-Kritrima-AI.bat      - Quick CLI launcher
echo    Kritrima-AI-Development.bat - Development environment
echo    Sync-to-OneDrive.bat        - Sync changes back
echo    Run-Examples.bat            - Run example projects
echo.
echo [INFO] Quick Start:
echo    1. Double-click "Launch-Kritrima-AI.bat" to use the CLI
echo    2. Double-click "Kritrima-AI-Development.bat" for development
echo    3. Double-click "Run-Examples.bat" to try examples
echo.
echo [INFO] Tips:
echo    - Project is located at: ~/projects/kritrima-ai (in WSL)
echo    - Use sync script to backup changes to OneDrive
echo    - All Node.js tools are available in WSL environment
echo.
echo Press any key to exit...
pause >nul
exit /b 0
