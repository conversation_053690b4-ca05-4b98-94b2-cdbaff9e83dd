{"version": 3, "file": "no-require-imports.js", "sourceRoot": "", "sources": ["../../src/rules/no-require-imports.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAAoE;AAEpE,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,oCAAoC;SAClD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,WAAW,EAAE,mDAAmD;qBACjE;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,gBAAgB,EAAE,0CAA0C;SAC7D;KACF;IACD,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,OAAO;QACrB,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CACxC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CACpC,CAAC;QACF,SAAS,mBAAmB,CAAC,UAAkB;YAC7C,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,SAAS,yBAAyB,CAAC,IAAmB;YACpD,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBACnC,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;gBACjC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,CAC7C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,uCAAuC,CACrC,IAA6B;gBAE7B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtE,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9D,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAClE,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CACpC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,EACjC,SAAS,CACV,CAAC;gBAEF,6EAA6E;gBAC7E,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;oBAClC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,kBAAkB;qBAC9B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,IAAI,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC5D,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAClE,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,kBAAkB;iBAC9B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}