@echo off
setlocal enabledelayedexpansion

:: ============================================================================
:: 🚀 KRITRIMA-AI AUTOMATED WSL SETUP SCRIPT
:: ============================================================================
:: Professional Windows 11 WSL automation script for Kritrima-AI development
:: Author: Kritrima-AI Team
:: Version: 1.0.0
::
:: This script automatically:
:: - Ensures WSL2 is installed and configured
:: - Sets up Node.js 22+ environment
:: - Installs all dependencies
:: - Builds the project
:: - Creates development shortcuts
:: - Provides easy launch commands
:: ============================================================================

title 🚀 Kritrima-AI WSL Setup Wizard

:: Color codes for enhanced output
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "MAGENTA=[95m"
set "CYAN=[96m"
set "WHITE=[97m"
set "RESET=[0m"

:: Configuration
set "PROJECT_NAME=Kritrima-AI"
set "WSL_DISTRO=Ubuntu"
set "NODE_VERSION=22"
set "SOURCE_PATH=%~dp0"
set "WSL_PROJECT_PATH=/home/<USER>/projects/kritrima-ai"
set "ONEDRIVE_PATH=%USERPROFILE%\OneDrive\Documents\Kritrima AI"

echo.
echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║                    🚀 KRITRIMA-AI WSL SETUP WIZARD                          ║%RESET%
echo %CYAN%║                         Professional Automation                             ║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo %RED%❌ Administrator privileges required!%RESET%
    echo %YELLOW%Please right-click this script and select "Run as administrator"%RESET%
    pause
    exit /b 1
)

echo %GREEN%✅ Running with administrator privileges%RESET%
echo.

:: Step 1: Check and install WSL2
echo %BLUE%📋 Step 1: Checking WSL2 installation...%RESET%
wsl --status >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%⚠️  WSL2 not found. Installing WSL2...%RESET%
    dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
    dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart

    echo %YELLOW%📥 Downloading WSL2 kernel update...%RESET%
    powershell -Command "Invoke-WebRequest -Uri 'https://wslstorestorage.blob.core.windows.net/wslblob/wsl_update_x64.msi' -OutFile '%TEMP%\wsl_update_x64.msi'"

    echo %YELLOW%🔧 Installing WSL2 kernel update...%RESET%
    msiexec /i "%TEMP%\wsl_update_x64.msi" /quiet /norestart

    wsl --set-default-version 2

    echo %GREEN%✅ WSL2 installed successfully%RESET%
    echo %YELLOW%⚠️  System restart required. Please restart and run this script again.%RESET%
    pause
    exit /b 0
) else (
    echo %GREEN%✅ WSL2 is already installed%RESET%
)

:: Step 2: Check and install Ubuntu
echo.
echo %BLUE%📋 Step 2: Checking Ubuntu distribution...%RESET%
wsl -l -v | findstr "Ubuntu" >nul 2>&1
if %errorLevel% neq 0 (
    echo %YELLOW%⚠️  Ubuntu not found. Installing Ubuntu...%RESET%
    wsl --install -d Ubuntu
    echo %GREEN%✅ Ubuntu installed successfully%RESET%
    echo %YELLOW%⚠️  Please complete Ubuntu setup and run this script again.%RESET%
    pause
    exit /b 0
) else (
    echo %GREEN%✅ Ubuntu distribution is available%RESET%
)

:: Step 3: Configure WSL for better file system performance
echo.
echo %BLUE%📋 Step 3: Configuring WSL for optimal performance...%RESET%
wsl -d Ubuntu -e bash -c "
if [ ! -f /etc/wsl.conf ]; then
    echo '[automount]' | sudo tee /etc/wsl.conf > /dev/null
    echo 'enabled = true' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'root = /mnt/' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'options = \"metadata,umask=22,fmask=11\"' | sudo tee -a /etc/wsl.conf > /dev/null
    echo '[interop]' | sudo tee -a /etc/wsl.conf > /dev/null
    echo 'appendWindowsPath = false' | sudo tee -a /etc/wsl.conf > /dev/null
fi
"
echo %GREEN%✅ WSL configuration optimized%RESET%

:: Step 4: Install Node.js and development tools
echo.
echo %BLUE%📋 Step 4: Setting up Node.js %NODE_VERSION% and development environment...%RESET%
wsl -d Ubuntu -e bash -c "
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential development tools
sudo apt install -y curl wget git build-essential

# Install Node Version Manager (nvm)
if [ ! -d ~/.nvm ]; then
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
    export NVM_DIR=\"\$HOME/.nvm\"
    [ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
fi

# Source nvm
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"

# Install Node.js %NODE_VERSION%
nvm install %NODE_VERSION%
nvm use %NODE_VERSION%
nvm alias default %NODE_VERSION%

# Install pnpm globally
npm install -g pnpm

# Verify installations
echo \"Node.js version: \$(node --version)\"
echo \"npm version: \$(npm --version)\"
echo \"pnpm version: \$(pnpm --version)\"
"
echo %GREEN%✅ Node.js environment configured%RESET%

:: Step 5: Copy project to WSL file system
echo.
echo %BLUE%📋 Step 5: Setting up Kritrima-AI project in WSL...%RESET%

:: Check if source exists
if not exist "%SOURCE_PATH%" (
    if exist "%ONEDRIVE_PATH%" (
        set "SOURCE_PATH=%ONEDRIVE_PATH%"
    ) else (
        echo %RED%❌ Project source not found!%RESET%
        echo %YELLOW%Please ensure the project is in the same directory as this script or in OneDrive%RESET%
        pause
        exit /b 1
    )
)

wsl -d Ubuntu -e bash -c "
# Create projects directory
mkdir -p ~/projects

# Remove existing project if it exists
if [ -d ~/projects/kritrima-ai ]; then
    rm -rf ~/projects/kritrima-ai
fi

# Copy project from Windows to WSL
echo 'Copying project files to WSL file system...'
cp -r '/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI' ~/projects/kritrima-ai

# Navigate to project directory
cd ~/projects/kritrima-ai

# Source nvm and set Node.js
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
nvm use %NODE_VERSION%

# Install project dependencies
echo 'Installing project dependencies...'
pnpm install

# Build the project
echo 'Building the project...'
pnpm run build

# Install CLI globally
echo 'Installing Kritrima-AI CLI globally...'
npm install -g .

echo 'Project setup completed successfully!'
"
echo %GREEN%✅ Kritrima-AI project configured in WSL%RESET%

:: Step 6: Create convenience scripts
echo.
echo %BLUE%📋 Step 6: Creating convenience scripts and shortcuts...%RESET%

:: Create launch script for Kritrima-AI
(
echo @echo off
echo title 🚀 Kritrima-AI CLI
echo echo.
echo echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo echo %CYAN%║                           🚀 KRITRIMA-AI CLI                                 ║%RESET%
echo echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo echo.
echo echo %GREEN%Welcome to Kritrima-AI! Enter your prompt below:%RESET%
echo echo %YELLOW%Example: "Create a simple web page with a contact form"%RESET%
echo echo.
echo set /p "prompt=💭 Your prompt: "
echo if "!prompt!"=="" ^(
echo     echo %RED%❌ No prompt provided%RESET%
echo     pause
echo     exit /b 1
echo ^)
echo echo.
echo echo %BLUE%🚀 Launching Kritrima-AI...%RESET%
echo wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai && export NVM_DIR='\$HOME/.nvm' && [ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh' && nvm use %NODE_VERSION% && kritrima-ai '!prompt!'"
echo pause
) > "🚀 Launch Kritrima-AI.bat"

:: Create development script
(
echo @echo off
echo title 🛠️ Kritrima-AI Development
echo echo.
echo echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo echo %CYAN%║                      🛠️ KRITRIMA-AI DEVELOPMENT                              ║%RESET%
echo echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo echo.
echo echo %GREEN%Starting development environment...%RESET%
echo wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai && export NVM_DIR='\$HOME/.nvm' && [ -s '\$NVM_DIR/nvm.sh' ] && \. '\$NVM_DIR/nvm.sh' && nvm use %NODE_VERSION% && echo 'Development commands:' && echo '  pnpm run dev     - Start development with watch mode' && echo '  pnpm run build   - Build the project' && echo '  pnpm test        - Run tests' && echo '  pnpm run lint    - Run linting' && echo '' && bash"
) > "🛠️ Kritrima-AI Development.bat"

:: Create sync script
(
echo @echo off
echo title 🔄 Sync to OneDrive
echo echo.
echo echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo echo %CYAN%║                        🔄 SYNC TO ONEDRIVE                                   ║%RESET%
echo echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo echo.
echo echo %YELLOW%Syncing changes back to OneDrive...%RESET%
echo wsl -d Ubuntu -e bash -c "rsync -av --exclude node_modules --exclude dist ~/projects/kritrima-ai/ '/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/' && echo 'Sync completed successfully!'"
echo echo %GREEN%✅ Sync completed!%RESET%
echo pause
) > "🔄 Sync to OneDrive.bat"

:: Create examples runner
(
echo @echo off
echo title 📚 Kritrima-AI Examples
echo echo.
echo echo %CYAN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo echo %CYAN%║                       📚 KRITRIMA-AI EXAMPLES                                ║%RESET%
echo echo %CYAN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo echo.
echo echo %GREEN%Available examples:%RESET%
echo echo %YELLOW%1. camerascii     - Webcam ASCII art%RESET%
echo echo %YELLOW%2. prompt-analyzer - Prompt analysis tool%RESET%
echo echo %YELLOW%3. impossible-pong - Game development%RESET%
echo echo.
echo set /p "choice=Select example (1-3): "
echo if "!choice!"=="1" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/camerascii && chmod +x run.sh && ./run.sh"
echo if "!choice!"=="2" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/prompt-analyzer && chmod +x run.sh && ./run.sh"
echo if "!choice!"=="3" wsl -d Ubuntu -e bash -c "cd ~/projects/kritrima-ai/examples/impossible-pong && chmod +x run.sh && ./run.sh"
echo pause
) > "📚 Run Examples.bat"

echo %GREEN%✅ Convenience scripts created%RESET%

:: Step 7: Final verification and testing
echo.
echo %BLUE%📋 Step 7: Final verification and testing...%RESET%
wsl -d Ubuntu -e bash -c "
cd ~/projects/kritrima-ai
export NVM_DIR=\"\$HOME/.nvm\"
[ -s \"\$NVM_DIR/nvm.sh\" ] && \. \"\$NVM_DIR/nvm.sh\"
nvm use %NODE_VERSION%

echo 'Verifying installation...'
echo 'Node.js version:' \$(node --version)
echo 'npm version:' \$(npm --version)
echo 'pnpm version:' \$(pnpm --version)
echo 'Kritrima-AI CLI:' \$(which kritrima-ai)

# Test CLI
echo 'Testing Kritrima-AI CLI...'
kritrima-ai --help > /dev/null 2>&1 && echo 'CLI test: PASSED' || echo 'CLI test: FAILED'
"

:: Success message and instructions
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%RESET%
echo %GREEN%║                           🎉 SETUP COMPLETED!                                ║%RESET%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%RESET%
echo.
echo %CYAN%📁 Created convenience scripts:%RESET%
echo %WHITE%   🚀 Launch Kritrima-AI.bat      - Quick CLI launcher%RESET%
echo %WHITE%   🛠️ Kritrima-AI Development.bat  - Development environment%RESET%
echo %WHITE%   🔄 Sync to OneDrive.bat         - Sync changes back%RESET%
echo %WHITE%   📚 Run Examples.bat             - Run example projects%RESET%
echo.
echo %CYAN%🚀 Quick Start:%RESET%
echo %WHITE%   1. Double-click "🚀 Launch Kritrima-AI.bat" to use the CLI%RESET%
echo %WHITE%   2. Double-click "🛠️ Kritrima-AI Development.bat" for development%RESET%
echo %WHITE%   3. Double-click "📚 Run Examples.bat" to try examples%RESET%
echo.
echo %CYAN%💡 Tips:%RESET%
echo %WHITE%   • Project is located at: ~/projects/kritrima-ai (in WSL)%RESET%
echo %WHITE%   • Use sync script to backup changes to OneDrive%RESET%
echo %WHITE%   • All Node.js tools are available in WSL environment%RESET%
echo.
echo %YELLOW%Press any key to exit...%RESET%
pause >nul
exit /b 0
