{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["../../../src/rules/enum-utils/shared.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,0CAMC;AAWD,oCAKC;AAgBD,oDAkCC;AA1GD,sDAAwC;AACxC,+CAAiC;AAEjC,qCAA2C;AAE3C;;;;;;;GAOG;AACH,SAAS,eAAe,CAAC,WAA2B,EAAE,IAAa;IACjE,oEAAoE;IACpE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAG,CAAC;IACjC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,WAAW,CAAC,iBAAiB,CACjC,MAAM,CAAC,gBAAkC,CAAC,MAAM,CAClD,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,eAAe,CAAC,IAAa;IAC3C,OAAO,OAAO;SACX,cAAc,CAAC,IAAI,CAAC;SACpB,MAAM,CAAC,CAAC,OAAO,EAA6B,EAAE,CAC7C,IAAA,oBAAa,EAAC,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CACjD,CAAC;AACN,CAAC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CAC1B,WAA2B,EAC3B,IAAa;IAEb,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/E,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,oBAAoB,CAClC,YAA8B,EAC9B,OAAgB;IAEhB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;QACvC,IAAI,WAAW,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;YAClC,MAAM,EAAE,MAAM,EAAE,GAAG,WAAW,CAAC;YAE/B,MAAM,iBAAiB,GAAG,MAAM,CAAC,gBAAiC,CAAC;YACnE,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,CAAC;YAEjD,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,IAAI,CAAC;YACpD,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC;YAE3C,QAAQ,oBAAoB,CAAC,IAAI,EAAE,CAAC;gBAClC,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;oBAC3B,OAAO,GAAG,QAAQ,IAAI,oBAAoB,CAAC,IAAI,EAAE,CAAC;gBAEpD,KAAK,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;oBACjC,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;oBAElE,OAAO,GAAG,QAAQ,KAAK,UAAU,IAAI,CAAC;gBACxC,CAAC;gBAED,KAAK,EAAE,CAAC,UAAU,CAAC,oBAAoB;oBACrC,OAAO,GAAG,QAAQ,IAAI,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC;gBAErE;oBACE,MAAM;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}