{"version": 3, "file": "use-text-input-state.js", "sourceRoot": "", "sources": ["../../../source/components/text-input/use-text-input-state.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAgB,OAAO,EAAC,MAAM,OAAO,CAAC;AA+BhF,MAAM,OAAO,GAA2B,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;IACzD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,kBAAkB,CAAC,CAAC,CAAC;YACzB,OAAO;gBACN,GAAG,KAAK;gBACR,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;aACjD,CAAC;QACH,CAAC;QAED,KAAK,mBAAmB,CAAC,CAAC,CAAC;YAC1B,OAAO;gBACN,GAAG,KAAK;gBACR,YAAY,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;aAClE,CAAC;QACH,CAAC;QAED,KAAK,QAAQ,CAAC,CAAC,CAAC;YACf,OAAO;gBACN,GAAG,KAAK;gBACR,aAAa,EAAE,KAAK,CAAC,KAAK;gBAC1B,KAAK,EACJ,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC;oBACxC,MAAM,CAAC,IAAI;oBACX,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC;gBACtC,YAAY,EAAE,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;aACrD,CAAC;QACH,CAAC;QAED,KAAK,QAAQ,CAAC,CAAC,CAAC;YACf,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YAE5D,OAAO;gBACN,GAAG,KAAK;gBACR,aAAa,EAAE,KAAK,CAAC,KAAK;gBAC1B,KAAK,EACJ,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC;oBACrC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC;gBACvC,YAAY,EAAE,eAAe;aAC7B,CAAC;QACH,CAAC;IACF,CAAC;AACF,CAAC,CAAC;AAwDF,MAAM,CAAC,MAAM,iBAAiB,GAAG,CAAC,EACjC,YAAY,GAAG,EAAE,EACjB,WAAW,EACX,QAAQ,EACR,QAAQ,GACgB,EAAE,EAAE;IAC5B,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE;QAC7C,aAAa,EAAE,YAAY;QAC3B,KAAK,EAAE,YAAY;QACnB,YAAY,EAAE,YAAY,CAAC,MAAM;KACjC,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE;QAC/B,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;QACR,CAAC;QAED,OAAO,WAAW;YACjB,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACxD,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IAC7B,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAE/B,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACvC,QAAQ,CAAC;YACR,IAAI,EAAE,kBAAkB;SACxB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;QACxC,QAAQ,CAAC;YACR,IAAI,EAAE,mBAAmB;SACzB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,IAAY,EAAE,EAAE;QAC3C,QAAQ,CAAC;YACR,IAAI,EAAE,QAAQ;YACd,IAAI;SACJ,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;QACxC,QAAQ,CAAC;YACR,IAAI,EAAE,QAAQ;SACd,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,MAAM,GAAG,WAAW,CAAC,GAAG,EAAE;QAC/B,IAAI,UAAU,EAAE,CAAC;YAChB,MAAM,CAAC,UAAU,CAAC,CAAC;YACnB,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,CAAC;YACrC,OAAO;QACR,CAAC;QAED,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEhD,SAAS,CAAC,GAAG,EAAE;QACd,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,aAAa,EAAE,CAAC;YACzC,QAAQ,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,CAAC;IACF,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;IAEjD,OAAO;QACN,GAAG,KAAK;QACR,UAAU;QACV,cAAc;QACd,eAAe;QACf,MAAM;QACN,MAAM,EAAE,eAAe;QACvB,MAAM;KACN,CAAC;AACH,CAAC,CAAC"}