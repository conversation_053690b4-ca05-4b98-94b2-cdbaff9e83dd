{"name": "@inkjs/ui", "version": "2.0.0", "description": "Collection of customizable UI components for CLIs made with Ink", "license": "MIT", "repository": "vadimdemedes/ink-ui", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "vadi<PERSON><PERSON><EMAIL>", "url": "https://github.com/vadimdemedes"}, "publishConfig": {"access": "public"}, "type": "module", "exports": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "engines": {"node": ">=18"}, "scripts": {"dev": "tsc --watch", "build": "tsc", "prepare": "npm run build", "test": "tsc --noEmit && xo && ava", "example": "NODE_NO_WARNINGS=1 node --import=tsimp"}, "files": ["build"], "dependencies": {"chalk": "^5.3.0", "cli-spinners": "^3.0.0", "deepmerge": "^4.3.1", "figures": "^6.1.0"}, "devDependencies": {"@sindresorhus/tsconfig": "^5.0.0", "@types/react": "^18.3.2", "@vdemedes/prettier-config": "^2.0.1", "ava": "^5.2.0", "boxen": "^7.1.1", "cat-names": "^4.0.0", "delay": "^6.0.0", "eslint-config-xo-react": "^0.27.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "ink": "^5.0.0", "ink-testing-library": "^4.0.0", "prettier": "^3.2.5", "react": "^18.3.1", "tsimp": "^2.0.11", "typescript": "^5.4.5", "xo": "^0.58.0"}, "peerDependencies": {"ink": ">=5"}, "ava": {"extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--import=tsimp"], "environmentVariables": {"NODE_NO_WARNINGS": "1", "FORCE_COLOR": "true"}}, "xo": {"extends": ["xo-react"], "plugins": ["react"], "prettier": true, "rules": {"react/no-unescaped-entities": "off", "unicorn/prevent-abbreviations": "off"}}, "prettier": "@vdemedes/prettier-config"}