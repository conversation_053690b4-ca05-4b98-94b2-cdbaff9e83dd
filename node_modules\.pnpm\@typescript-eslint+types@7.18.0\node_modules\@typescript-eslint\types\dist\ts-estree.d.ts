import type * as TSESTree from './generated/ast-spec';
declare module './generated/ast-spec' {
    interface BaseNode {
        parent: TSESTree.Node;
    }
    interface Program {
        /**
         * @remarks This never-used property exists only as a convenience for code that tries to access node parents repeatedly.
         */
        parent?: never;
    }
    interface AccessorPropertyComputedName {
        parent: TSESTree.ClassBody;
    }
    interface AccessorPropertyNonComputedName {
        parent: TSESTree.ClassBody;
    }
}
export * as TSESTree from './generated/ast-spec';
//# sourceMappingURL=ts-estree.d.ts.map