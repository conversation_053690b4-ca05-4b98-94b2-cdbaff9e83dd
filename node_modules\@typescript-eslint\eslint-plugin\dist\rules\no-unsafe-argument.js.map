{"version": 3, "file": "no-unsafe-argument.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-argument.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAG1D,kCAQiB;AA8BjB,MAAM,iBAAiB;IAGd,MAAM,CAAC,MAAM,CAClB,OAAuB,EACvB,MAA6B;QAE7B,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,UAAU,GAAc,EAAE,CAAC;QACjC,IAAI,QAAQ,GAAoB,IAAI,CAAC;QAErC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE9D,MAAM,IAAI,GAAG,KAAK,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,IAAI,IAAI,IAAA,iCAA0B,EAAC,IAAI,CAAC,EAAE,CAAC;gBAC7C,kBAAkB;gBAClB,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,QAAQ,GAAG;wBACT,IAAI,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACvC,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;qBAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrC,QAAQ,GAAG;wBACT,aAAa,EAAE,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC;wBAC7C,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,QAAQ,GAAG;wBACT,IAAI;wBACJ,IAAI,4BAAoB;wBACxB,KAAK,EAAE,CAAC;qBACT,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAID,YACU,UAAqB,EACrB,QAAyB;QADzB,eAAU,GAAV,UAAU,CAAW;QACrB,aAAQ,GAAR,QAAQ,CAAiB;QAtD3B,uBAAkB,GAAG,CAAC,CAAC;QAkDvB,yBAAoB,GAAG,KAAK,CAAC;IAKlC,CAAC;IAEG,oBAAoB;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC;QACtC,IAAI,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAE7B,IAAI,KAAK,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YACjE,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC1B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAC3B,+BAAuB,CAAC,CAAC,CAAC;oBACxB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAClD,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;wBAC9B,gEAAgE;wBAChE,uEAAuE;wBACvE,6CAA6C;wBAC7C,wDAAwD;wBACxD,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjD,CAAC;oBAED,MAAM,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;oBAC9C,IAAI,SAAS,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;wBACtC,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBACjD,CAAC;oBAED,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC;gBAClC,CAAC;gBAED,gCAAwB;gBACxB;oBACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAChC,CAAC;IAEM,yBAAyB;QAC9B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;IACnC,CAAC;CACF;AAED,kBAAe,IAAA,iBAAU,EAAiB;IACxC,IAAI,EAAE,oBAAoB;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0DAA0D;YACvE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,sFAAsF;YACxF,iBAAiB,EACf,4HAA4H;YAC9H,iBAAiB,EAAE,uCAAuC;YAC1D,YAAY,EAAE,iCAAiC;SAChD;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,oBAAoB,CAC3B,IAA+D,EAC/D,MAA2B,EAC3B,IAGqC;YAErC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,OAAO;YACT,CAAC;YAED,+DAA+D;YAC/D,IAAI,IAAA,oBAAa,EAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;gBACtD,OAAO;YACT,CAAC;YAED,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,SAAS,GAAG,IAAA,iBAAU,EAC1B,iBAAiB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EACzC,kCAAkC,CACnC,CAAC;YAEF,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,EAAE,CAAC;gBAC1D,4GAA4G;gBAC5G,SAAS,CAAC,oBAAoB,EAAE,CAAC;YACnC,CAAC;YAED,KAAK,MAAM,QAAQ,IAAI,IAAI,EAAE,CAAC;gBAC5B,QAAQ,QAAQ,CAAC,IAAI,EAAE,CAAC;oBACtB,kBAAkB;oBAClB,KAAK,sBAAc,CAAC,aAAa,CAAC,CAAC,CAAC;wBAClC,MAAM,aAAa,GAAG,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBAEpE,IAAI,IAAA,oBAAa,EAAC,aAAa,CAAC,EAAE,CAAC;4BACjC,cAAc;4BACd,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,cAAc;6BAC1B,CAAC,CAAC;wBACL,CAAC;6BAAM,IAAI,IAAA,yBAAkB,EAAC,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;4BACtD,gBAAgB;4BAEhB,yFAAyF;4BACzF,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,mBAAmB;6BAC/B,CAAC,CAAC;wBACL,CAAC;6BAAM,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;4BAC9C,2BAA2B;4BAC3B,MAAM,mBAAmB,GACvB,OAAO,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;4BAC1C,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE,CAAC;gCAC5C,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;gCACvD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;oCAC1B,SAAS;gCACX,CAAC;gCACD,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,SAAS,EACT,aAAa,EACb,OAAO;gCACP,mGAAmG;gCACnG,qBAAqB;gCACrB,IAAI,CACL,CAAC;gCACF,IAAI,MAAM,EAAE,CAAC;oCACX,OAAO,CAAC,MAAM,CAAC;wCACb,IAAI,EAAE,QAAQ;wCACd,SAAS,EAAE,mBAAmB;wCAC9B,IAAI,EAAE;4CACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;4CACvC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;yCAC9C;qCACF,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC;4BACD,IAAI,aAAa,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;gCACxC,gGAAgG;gCAChG,mFAAmF;gCACnF,SAAS,CAAC,yBAAyB,EAAE,CAAC;4BACxC,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,4BAA4B;4BAC5B,iEAAiE;4BACjE,sCAAsC;wBACxC,CAAC;wBACD,MAAM;oBACR,CAAC;oBAED,OAAO,CAAC,CAAC,CAAC;wBACR,MAAM,aAAa,GAAG,SAAS,CAAC,oBAAoB,EAAE,CAAC;wBACvD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;4BAC1B,SAAS;wBACX,CAAC;wBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;wBAC1D,MAAM,MAAM,GAAG,IAAA,yBAAkB,EAC/B,YAAY,EACZ,aAAa,EACb,OAAO,EACP,QAAQ,CACT,CAAC;wBACF,IAAI,MAAM,EAAE,CAAC;4BACX,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE;oCACJ,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;oCAC1C,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;iCAC9C;6BACF,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,+BAA+B,CAC7B,IAAsD;gBAEtD,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC1D,CAAC;YACD,wBAAwB,CAAC,IAAuC;gBAC9D,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YAC/D,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}